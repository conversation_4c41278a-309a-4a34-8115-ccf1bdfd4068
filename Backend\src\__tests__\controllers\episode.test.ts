import { describe, it, expect, vi, beforeEach } from "vitest";
import request from "supertest";
import app from "../../app";
import { Episode } from "../../models";
import { mockEpisodesPodcast1, mockUsers } from "../../utils/mock";

beforeEach(() => {
  vi.restoreAllMocks();
});

describe("Episode API Endpoints", () => {
  // ---------------- GET /episodes ----------------
  describe("GET /episodes", () => {
    it("should return paginated list of episodes", async () => {
      const paginateMock = {
        totalDocs: mockEpisodesPodcast1.length,
        totalPages: 1,
        page: 1,
        docs: mockEpisodesPodcast1,
      };

      vi.spyOn(Episode, "paginate").mockResolvedValue(paginateMock as any);

      const res = await request(app).get("/api/v1/episodes?page=1&limit=10");

      expect(res.status).toBe(200);
      expect(res.body.totalEpisodes).toBe(mockEpisodesPodcast1.length);
      expect(res.body.episodes[0].title).toBe(mockEpisodesPodcast1[0].title);
    });
  });

  // ---------------- GET /episodes/:id ----------------
  describe("GET /episodes/:id", () => {
    it("should return a single episode", async () => {
      vi.spyOn(Episode, "findById").mockResolvedValue(mockEpisodesPodcast1[0] as any);

      const res = await request(app).get(`/api/v1/episodes/${mockEpisodesPodcast1[0]._id}`);

      expect(res.status).toBe(200);
      expect(res.body.title).toBe(mockEpisodesPodcast1[0].title);
    });
  });
});     
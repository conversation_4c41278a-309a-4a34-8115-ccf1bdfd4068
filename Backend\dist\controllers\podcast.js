"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const index_1 = require("../models/index");
const mongoose_1 = __importDefault(require("mongoose"));
const constants_1 = require("../utils/constants");
const aws_1 = require("../utils/aws");
const errorMiddleware_1 = require("../middlewares/errorMiddleware");
const error_1 = require("../utils/error");
/**
 * @desc Get all podcasts (paginated)
 * @route GET /podcasts
 * @access Public
 */
const getAllPodcasts = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { page = "1", limit = "10" } = req.query;
    const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort: { createdAt: -1 },
        select: "title author description category coverImageUrl createdAt",
    };
    const podcasts = await index_1.Podcast.paginate({}, options);
    res.status(200).json({
        status: 'success',
        totalPodcasts: podcasts.totalDocs,
        totalPages: podcasts.totalPages,
        currentPage: podcasts.page,
        podcasts: podcasts.docs,
    });
});
/**
 * @desc Get a single podcast by ID (with its episodes)
 * @route GET /podcasts/:id
 * @access Public
 */
const getPodcastById = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const podcast = await index_1.Podcast.findById(id).populate({
        path: "episodes",
        select: "title description audioUrl duration createdAt",
        options: { sort: { createdAt: -1 } },
    });
    if (!podcast) {
        throw new error_1.NotFoundError("Podcast not found");
    }
    res.status(200).json({
        status: 'success',
        data: {
            podcast
        }
    });
});
/**
 * @desc Search podcasts by title or filter by category (paginated)
 * @route GET /podcasts/search?query=react&category=Technology&page=1&limit=5
 * @access Public
 */
const podcastSearchAndFilterByTitleOrCategory = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { query = "", category, page = "1", limit = "10" } = req.query;
    const filter = {};
    if (query) {
        filter.title = { $regex: query, $options: "i" };
    }
    if (category && category !== "All") {
        filter.category = category;
    }
    const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort: { createdAt: -1 },
        select: "title author description category coverImageUrl createdAt",
    };
    const podcasts = await index_1.Podcast.paginate(filter, options);
    res.status(200).json({
        status: 'success',
        totalResults: podcasts.totalDocs,
        totalPages: podcasts.totalPages,
        currentPage: podcasts.page,
        podcasts: podcasts.docs,
    });
});
/**
 * @desc User requests to upload a podcast (saved as pending)
 * @route POST /podcasts/request-upload
 * @access Public
 */
const requestToUploadPodcastByUser = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { title, description, author, category, coverImageUrl, userId } = req.body;
    const user = await index_1.User.findById(userId);
    if (!user) {
        throw new error_1.NotFoundError("User not found");
    }
    const podcast = await index_1.Podcast.create({
        title,
        description,
        author,
        category,
        coverImageUrl,
        status: constants_1.Status.PENDING,
    });
    res.status(201).json({
        status: 'success',
        message: "Request to upload podcast submitted successfully",
        data: {
            podcast: {
                id: podcast._id,
                title: podcast.title,
                status: podcast.status,
                createdAt: podcast.createdAt
            }
        }
    });
});
/**
 * @desc Admin approves or rejects a podcast request
 * @route PATCH /podcasts/:id/approve-reject
 * @access Admin
 */
const adminApproveOrRejectPodcast = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const session = await mongoose_1.default.startSession();
    try {
        const { id } = req.params;
        const { status, adminId } = req.body;
        // Validate status
        const validStatuses = [constants_1.Status.APPROVED, constants_1.Status.REJECTED];
        if (!validStatuses.includes(status)) {
            throw new error_1.ValidationError("Invalid status. Must be 'approved' or 'rejected'");
        }
        if (!adminId) {
            throw new error_1.ValidationError("Admin ID is required");
        }
        // Check admin exists and has admin role
        const admin = await index_1.User.findById(adminId);
        if (!admin) {
            throw new error_1.NotFoundError("Admin not found");
        }
        if (admin.role !== constants_1.UserRole.ADMIN) {
            throw new error_1.ForbiddenError("Access denied. Admin role required");
        }
        // Start transaction
        session.startTransaction();
        // Fetch podcast and its episodes
        const podcast = await index_1.Podcast.findById(id).populate("episodes").session(session);
        if (!podcast) {
            throw new error_1.NotFoundError("Podcast not found");
        }
        // If approved, move all episodes from TEMP to PERM bucket
        if (status === constants_1.Status.APPROVED) {
            for (const episode of podcast.episodes) {
                if (episode.audioKey) {
                    try {
                        // Move audio file and get public URL
                        const audioUrl = await (0, aws_1.moveObjectToPermanentBucket)(episode.audioKey);
                        // Update episode with approved URL
                        episode.audioUrl = audioUrl;
                        await episode.save({ session });
                    }
                    catch (awsError) {
                        console.error(`Error moving episode ${episode._id} to permanent bucket:`, awsError);
                        throw new error_1.AWSError(`Failed to move episode ${episode._id} to permanent storage`);
                    }
                }
            }
            podcast.status = constants_1.Status.APPROVED;
        }
        else if (status === constants_1.Status.REJECTED) {
            podcast.status = constants_1.Status.REJECTED;
        }
        await podcast.save({ session });
        await session.commitTransaction();
        res.status(200).json({
            status: 'success',
            message: `Podcast ${status.toLowerCase()} successfully`,
            data: {
                podcast: {
                    id: podcast._id,
                    title: podcast.title,
                    status: podcast.status,
                    updatedAt: podcast.updatedAt
                }
            }
        });
    }
    catch (error) {
        await session.abortTransaction();
        throw error; // Re-throw to be handled by global error handler
    }
    finally {
        await session.endSession();
    }
});
exports.default = {
    getAllPodcasts,
    getPodcastById,
    podcastSearchAndFilterByTitleOrCategory,
    requestToUploadPodcastByUser,
    adminApproveOrRejectPodcast,
};

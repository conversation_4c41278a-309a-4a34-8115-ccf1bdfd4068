"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const podcastRoutes_1 = __importDefault(require("./podcastRoutes"));
const episodeRoutes_1 = __importDefault(require("./episodeRoutes"));
const router = (0, express_1.Router)();
router.use("/podcasts", podcastRoutes_1.default);
router.use("/episodes", episodeRoutes_1.default);
exports.default = router;

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const index_1 = require("../models/index");
const aws_1 = require("../utils/aws");
const errorMiddleware_1 = require("../middlewares/errorMiddleware");
const error_1 = require("../utils/error");
/**
 * @desc Get all episodes (paginated)
 * @route GET /episodes
 * @access Public
 */
const getAllEpisodes = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { page = "1", limit = "10" } = req.query;
    const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort: { createdAt: -1 },
        select: "title description audioUrl duration createdAt",
    };
    const episodes = await index_1.Episode.paginate({}, options);
    res.status(200).json({
        status: 'success',
        totalEpisodes: episodes.totalDocs,
        totalPages: episodes.totalPages,
        currentPage: episodes.page,
        episodes: episodes.docs,
    });
});
/**
 * @desc Get an episode by ID
 * @route GET /episodes/:id
 * @access Public
 */
const getEpisodeById = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const episode = await index_1.Episode.findById(id);
    if (!episode) {
        throw new error_1.NotFoundError("Episode not found");
    }
    res.status(200).json({
        status: 'success',
        data: {
            episode
        }
    });
});
/**
 * @desc Generate pre-signed URL for episode audio upload
 * @route POST /episodes/upload-url
 * @access User
 */
const requestForPreSignedUrlForEpisode = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { filename, contentType } = req.body;
    try {
        const response = await (0, aws_1.generateUploadUrl)(filename, contentType);
        res.status(200).json({
            status: 'success',
            message: "Pre-signed URL generated successfully",
            data: {
                url: response.url,
                key: response.key
            }
        });
    }
    catch (error) {
        throw new error_1.AWSError("Failed to generate upload URL");
    }
});
/**
 * @desc Add an episode to a podcast
 * @route POST /podcasts/:id/episodes
 * @access User
 */
const addEpisodeToPodcast = (0, errorMiddleware_1.catchAsync)(async (req, res) => {
    const { id } = req.params;
    const { title, description, audioKey, duration } = req.body;
    const podcast = await index_1.Podcast.findById(id);
    if (!podcast) {
        throw new error_1.NotFoundError("Podcast not found");
    }
    const episode = await index_1.Episode.create({
        title,
        description,
        audioKey,
        duration,
        podcast: id,
    });
    podcast.episodes.push(episode._id);
    await podcast.save();
    res.status(201).json({
        status: 'success',
        message: 'Episode added to podcast successfully',
        data: {
            episode
        }
    });
});
exports.default = {
    getAllEpisodes,
    getEpisodeById,
    requestForPreSignedUrlForEpisode,
    addEpisodeToPodcast,
};

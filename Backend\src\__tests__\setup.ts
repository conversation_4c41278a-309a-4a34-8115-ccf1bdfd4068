import { vi } from "vitest";

// Mock environment variables for testing
process.env.NODE_ENV = "test";
process.env.PORT = "3000";
process.env.FRONTEND_URL = "http://localhost:3000";
process.env.MONGODB_URI = "mongodb://localhost:27017/test";
process.env.AWS_ACCESS_KEY_ID = "test-access-key";
process.env.AWS_SECRET_ACCESS_KEY = "test-secret-key";
process.env.AWS_TEMP_BUCKET_NAME = "test-temp-bucket";
process.env.AWS_PREM_BUCKET_NAME = "test-perm-bucket";
process.env.AWS_REGION = "us-east-1";

// Mock AWS utilities
vi.mock("../utils/aws", () => ({
  generateUploadUrl: vi.fn().mockResolvedValue({
    url: "https://s3.amazonaws.com/fakebucket/fakefile.mp3",
    key: "pending/fake-key",
  }),
  moveObjectToPermanentBucket: vi.fn().mockImplementation((key: string) => 
    Promise.resolve(`https://example.com/perm/${key}`)
  ),
}));

// Mock mongoose connection
vi.mock("mongoose", async () => {
  const actual = await vi.importActual("mongoose");
  return {
    ...actual,
    connect: vi.fn().mockResolvedValue({}),
    startSession: vi.fn().mockResolvedValue({
      startTransaction: vi.fn(),
      commitTransaction: vi.fn(),
      abortTransaction: vi.fn(),
      endSession: vi.fn(),
    }),
  };
});

// User Types Enum
enum UserRole {
  USER = "user",
  ADMIN = "admin",
}

// Podcast Types Enum
enum PodcastCategory {
  TECHNOLOGY = "Technology",
  EDUCATION = "Education",
  HEALTH = "Health",
  BUSINESS = "Business",
  ENTERTAINMENT = "Entertainment",
  OTHER = "Other",
}

enum Status {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}


export {
  UserRole,
  PodcastCategory,
  Status,
};

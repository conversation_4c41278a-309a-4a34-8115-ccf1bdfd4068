"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
// Set test environment first
process.env.NODE_ENV = "test";
// Mock environment variables for testing
process.env.PORT = "3000";
process.env.FRONTEND_URL = "http://localhost:3000";
process.env.MONGODB_URI = "mongodb://localhost:27017/test";
process.env.AWS_ACCESS_KEY_ID = "test-access-key";
process.env.AWS_SECRET_ACCESS_KEY = "test-secret-key";
process.env.AWS_TEMP_BUCKET_NAME = "test-temp-bucket";
process.env.AWS_PREM_BUCKET_NAME = "test-perm-bucket";
process.env.AWS_REGION = "us-east-1";
// Mock AWS SDK completely to prevent any real AWS calls
vitest_1.vi.mock("aws-sdk", () => ({
    S3: vitest_1.vi.fn().mockImplementation(() => ({
        getSignedUrl: vitest_1.vi.fn().mockReturnValue("https://s3.amazonaws.com/fakebucket/fakefile.mp3"),
        copyObject: vitest_1.vi.fn().mockReturnValue({
            promise: vitest_1.vi.fn().mockResolvedValue({}),
        }),
        deleteObject: vitest_1.vi.fn().mockReturnValue({
            promise: vitest_1.vi.fn().mockResolvedValue({}),
        }),
    })),
    config: {
        update: vitest_1.vi.fn(),
    },
}));
// Mock AWS utilities
vitest_1.vi.mock("../utils/aws", () => ({
    generateUploadUrl: vitest_1.vi.fn().mockResolvedValue({
        url: "https://s3.amazonaws.com/fakebucket/fakefile.mp3",
        key: "pending/fake-key",
    }),
    moveObjectToPermanentBucket: vitest_1.vi.fn().mockImplementation((key) => Promise.resolve(`https://example.com/perm/${key}`)),
}));
// Mock environment validation to prevent startup errors
vitest_1.vi.mock("../utils/envSchema", () => ({
    default: {
        PORT: 3000,
        NODE_ENV: "test",
        FRONTEND_URL: "http://localhost:3000",
        MONGODB_URI: "mongodb://localhost:27017/test",
        AWS_ACCESS_KEY_ID: "test-access-key",
        AWS_SECRET_ACCESS_KEY: "test-secret-key",
        AWS_TEMP_BUCKET_NAME: "test-temp-bucket",
        AWS_PREM_BUCKET_NAME: "test-perm-bucket",
        AWS_REGION: "us-east-1",
    },
}));
// Mock mongoose connection
vitest_1.vi.mock("mongoose", async () => {
    const actual = await vitest_1.vi.importActual("mongoose");
    return {
        ...actual,
        connect: vitest_1.vi.fn().mockResolvedValue({}),
        startSession: vitest_1.vi.fn().mockResolvedValue({
            startTransaction: vitest_1.vi.fn(),
            commitTransaction: vitest_1.vi.fn(),
            abortTransaction: vitest_1.vi.fn(),
            endSession: vitest_1.vi.fn(),
        }),
    };
});
// Global test setup
(0, vitest_1.beforeEach)(() => {
    vitest_1.vi.clearAllMocks();
});

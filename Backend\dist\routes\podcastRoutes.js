"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const podcast_1 = __importDefault(require("../controllers/podcast"));
const episode_1 = __importDefault(require("../controllers/episode"));
const express_1 = require("express");
const { getAllPodcasts, getPodcastById, podcastSearchAndFilterByTitleOrCategory, requestToUploadPodcastByUser, adminApproveOrRejectPodcast } = podcast_1.default;
const { addEpisodeToPodcast } = episode_1.default;
const podcastRouter = (0, express_1.Router)();
// Public routes
podcastRouter.get("/", getAllPodcasts);
podcastRouter.get("/search/filter", podcastSearchAndFilterByTitleOrCategory);
podcastRouter.get("/:id", getPodcastById);
// User routes
podcastRouter.post("/request-upload", requestToUploadPodcastByUser);
podcastRouter.post("/:id/episodes", addEpisodeToPodcast);
// Admin routes
podcastRouter.patch("/:id/status", adminApproveOrRejectPodcast);
exports.default = podcastRouter;

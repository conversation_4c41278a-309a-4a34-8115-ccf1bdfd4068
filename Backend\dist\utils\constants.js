"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Status = exports.PodcastCategory = exports.UserRole = void 0;
// User Types Enum
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "user";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
// Podcast Types Enum
var PodcastCategory;
(function (PodcastCategory) {
    PodcastCategory["TECHNOLOGY"] = "Technology";
    PodcastCategory["EDUCATION"] = "Education";
    PodcastCategory["HEALTH"] = "Health";
    PodcastCategory["BUSINESS"] = "Business";
    PodcastCategory["ENTERTAINMENT"] = "Entertainment";
    PodcastCategory["OTHER"] = "Other";
})(PodcastCategory || (exports.PodcastCategory = PodcastCategory = {}));
var Status;
(function (Status) {
    Status["PENDING"] = "pending";
    Status["APPROVED"] = "approved";
    Status["REJECTED"] = "rejected";
})(Status || (exports.Status = Status = {}));

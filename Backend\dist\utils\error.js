"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TooManyRequestsError = exports.BadRequestError = exports.ConflictError = exports.AWSError = exports.DatabaseError = exports.InternalServerError = exports.ForbiddenError = exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AppError = void 0;
class AppError extends Error {
    constructor(message, statusCode) {
        super(message);
        // Set custom properties
        this.statusCode = statusCode;
        this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
        this.isOperational = true;
        this.name = this.constructor.name;
        // Maintains proper stack trace (only in V8 engines like Node)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message = "Validation failed") {
        super(message, 400);
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends AppError {
    constructor(message = "Not found") {
        super(message, 404);
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends AppError {
    constructor(message = "Unauthorized") {
        super(message, 401);
    }
}
exports.UnauthorizedError = UnauthorizedError;
class ForbiddenError extends AppError {
    constructor(message = "Forbidden") {
        super(message, 403);
    }
}
exports.ForbiddenError = ForbiddenError;
class InternalServerError extends AppError {
    constructor(message = "Internal server error") {
        super(message, 500);
    }
}
exports.InternalServerError = InternalServerError;
class DatabaseError extends AppError {
    constructor(message = "Database operation failed") {
        super(message, 500);
    }
}
exports.DatabaseError = DatabaseError;
class AWSError extends AppError {
    constructor(message = "AWS operation failed") {
        super(message, 500);
    }
}
exports.AWSError = AWSError;
class ConflictError extends AppError {
    constructor(message = "Resource conflict") {
        super(message, 409);
    }
}
exports.ConflictError = ConflictError;
class BadRequestError extends AppError {
    constructor(message = "Bad request") {
        super(message, 400);
    }
}
exports.BadRequestError = BadRequestError;
class TooManyRequestsError extends AppError {
    constructor(message = "Too many requests") {
        super(message, 429);
    }
}
exports.TooManyRequestsError = TooManyRequestsError;

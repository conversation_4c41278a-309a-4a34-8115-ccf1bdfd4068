"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const episode_1 = __importDefault(require("../controllers/episode"));
const express_1 = require("express");
const { getAllEpisodes, getEpisodeById, requestForPreSignedUrlForEpisode, } = episode_1.default;
const episodeRouter = (0, express_1.Router)();
// Episode routes
episodeRouter.get("/", getAllEpisodes);
episodeRouter.get("/:id", getEpisodeById);
episodeRouter.post("/upload-url", requestForPreSignedUrlForEpisode);
// Note: addEpisodeToPodcast will be handled in podcast routes as it's a nested resource
// POST /podcasts/:id/episodes
exports.default = episodeRouter;

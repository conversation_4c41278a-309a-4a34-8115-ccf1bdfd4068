"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testApp_1 = __importDefault(require("./testApp"));
const vitest_1 = require("vitest");
(0, vitest_1.test)("GET /health should return 200 OK", async () => {
    const response = await (0, supertest_1.default)(testApp_1.default).get("/health");
    (0, vitest_1.expect)(response.status).toBe(200);
});

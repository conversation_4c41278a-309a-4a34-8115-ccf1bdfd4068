"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const zod_1 = __importDefault(require("zod"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables from the .env file
dotenv_1.default.config();
const isDev = process.env.NODE_ENV === "development";
// Define schema for environment variables
const envSchema = zod_1.default.object({
    // Server configuration
    PORT: zod_1.default.string().min(1).max(65535).transform(Number),
    NODE_ENV: zod_1.default.string().min(1),
    FRONTEND_URL: zod_1.default.string().url(),
    // Database configuration
    MONGODB_URI: zod_1.default.string().url(),
    // AWS configuration (conditionally required)
    AWS_ACCESS_KEY_ID: isDev
        ? zod_1.default.string().optional()
        : zod_1.default.string().min(1, "AWS_ACCESS_KEY_ID is required in non-dev environments"),
    AWS_SECRET_ACCESS_KEY: isDev
        ? zod_1.default.string().optional()
        : zod_1.default.string().min(1, "AWS_SECRET_ACCESS_KEY is required in non-dev environments"),
    AWS_TEMP_BUCKET_NAME: isDev
        ? zod_1.default.string().optional()
        : zod_1.default.string().min(1, "AWS_TEMP_BUCKET_NAME is required in non-dev environments"),
    AWS_PREM_BUCKET_NAME: isDev
        ? zod_1.default.string().optional()
        : zod_1.default.string().min(1, "AWS_PREM_BUCKET_NAME is required in non-dev environments"),
    AWS_REGION: zod_1.default.string().min(1, "AWS_REGION is required"),
});
/**
 * Validates the environment variables against the defined schema.
 * Throws an error at startup if any required variable is missing or invalid.
 * @param {NodeJS.ProcessEnv} env - The environment object to validate.
 * @returns {object} The validated environment variables with correct types.
 */
const validatedEnv = envSchema.safeParse(process.env);
if (!validatedEnv.success) {
    console.error("❌ Invalid environment variables:", validatedEnv.error.flatten().fieldErrors);
    throw new Error("Invalid environment configuration");
}
exports.default = validatedEnv.data;

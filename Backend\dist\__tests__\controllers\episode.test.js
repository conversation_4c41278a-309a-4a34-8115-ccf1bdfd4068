"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const supertest_1 = __importDefault(require("supertest"));
const testApp_1 = __importDefault(require("../testApp"));
const models_1 = require("../../models");
const mock_1 = require("../../utils/mock");
(0, vitest_1.beforeEach)(() => {
    vitest_1.vi.clearAllMocks();
});
(0, vitest_1.describe)("Episode API Endpoints", () => {
    // ---------------- GET /episodes ----------------
    (0, vitest_1.describe)("GET /episodes", () => {
        (0, vitest_1.it)("should return paginated list of episodes", async () => {
            const paginateMock = {
                totalDocs: mock_1.mockEpisodesPodcast1.length,
                totalPages: 1,
                page: 1,
                docs: mock_1.mockEpisodesPodcast1,
            };
            vitest_1.vi.spyOn(models_1.Episode, "paginate").mockResolvedValue(paginateMock);
            const res = await (0, supertest_1.default)(testApp_1.default).get("/api/v1/episodes?page=1&limit=10");
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.totalEpisodes).toBe(mock_1.mockEpisodesPodcast1.length);
            (0, vitest_1.expect)(res.body.episodes[0].title).toBe(mock_1.mockEpisodesPodcast1[0].title);
        });
    });
    // ---------------- GET /episodes/:id ----------------
    (0, vitest_1.describe)("GET /episodes/:id", () => {
        (0, vitest_1.it)("should return a single episode", async () => {
            vitest_1.vi.spyOn(models_1.Episode, "findById").mockResolvedValue(mock_1.mockEpisodesPodcast1[0]);
            const res = await (0, supertest_1.default)(testApp_1.default).get(`/api/v1/episodes/${mock_1.mockEpisodesPodcast1[0]._id}`);
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.status).toBe("success");
            (0, vitest_1.expect)(res.body.data.episode.title).toBe(mock_1.mockEpisodesPodcast1[0].title);
        });
    });
    // ---------------- POST /:id/episodes ----------------
    (0, vitest_1.describe)("POST /:id/episodes", () => {
        (0, vitest_1.it)("should add an episode to a podcast", async () => {
            const mockPodcast = {
                ...mock_1.mockPodcasts[0],
                episodes: [],
                save: vitest_1.vi.fn().mockResolvedValue(true),
            };
            vitest_1.vi.spyOn(models_1.Podcast, "findById").mockResolvedValue(mockPodcast);
            vitest_1.vi.spyOn(models_1.Episode, "create").mockResolvedValue({
                ...mock_1.mockEpisodesPodcast1[0],
                title: "New Episode",
            });
            const res = await (0, supertest_1.default)(testApp_1.default)
                .post(`/api/v1/podcasts/${mock_1.mockPodcasts[0]._id}/episodes`)
                .send({
                title: "New Episode",
                description: "Description",
                audioKey: "audio/key.mp3",
                duration: 180,
            });
            (0, vitest_1.expect)(res.status).toBe(201);
            (0, vitest_1.expect)(res.body.status).toBe("success");
            (0, vitest_1.expect)(res.body.data.episode.title).toBe("New Episode");
        });
    });
    // ---------------- POST /episodes/upload-url ----------------
    (0, vitest_1.describe)("POST /episodes/upload-url", () => {
        (0, vitest_1.it)("should generate a pre-signed URL for episode audio upload", async () => {
            const res = await (0, supertest_1.default)(testApp_1.default)
                .post("/api/v1/episodes/upload-url")
                .send({
                filename: "audio.mp3",
                contentType: "audio/mpeg",
            });
            (0, vitest_1.expect)(res.status).toBe(200);
            (0, vitest_1.expect)(res.body.status).toBe("success");
            (0, vitest_1.expect)(res.body.data.url).toBe("https://s3.amazonaws.com/fakebucket/fakefile.mp3");
            (0, vitest_1.expect)(res.body.data.key).toBe("pending/fake-key");
        });
    });
});

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const routes_1 = __importDefault(require("../routes"));
const errorMiddleware_1 = require("../middlewares/errorMiddleware");
const error_1 = require("../utils/error");
const app = (0, express_1.default)();
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'test',
    });
});
// API routes
app.use('/api/v1', routes_1.default);
// 404 handler - simplified for tests
app.use((req, res, next) => {
    next(new error_1.NotFoundError(`Route ${req.originalUrl} not found`));
});
// Global error handler - must be last middleware
app.use(errorMiddleware_1.globalErrorHandler);
exports.default = app;

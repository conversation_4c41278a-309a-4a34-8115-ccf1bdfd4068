"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const routes_1 = __importDefault(require("./routes"));
const errorMiddleware_1 = require("./middlewares/errorMiddleware");
const error_1 = require("./utils/error");
const app = (0, express_1.default)();
// CORS configuration
// const corsOptions = {
//   origin: process.env.NODE_ENV === 'production' 
//     ? process.env.FRONTEND_URL || "https://podcast-hub-seven.vercel.app"
//     : "*", // Allow all origins in development
//   credentials: true,
//   optionsSuccessStatus: 200, // Some legacy browsers (IE11, various SmartTVs) choke on 204
// };
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json({ limit: '10mb' })); // Increased limit for file uploads
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Request logging middleware (development only)
if (process.env.NODE_ENV !== 'production') {
    app.use((req, res, next) => {
        console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
        next();
    });
}
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
    });
});
// API routes
app.use('/api/v1', routes_1.default);
// 404 handler - must be after all routes
app.use((req, res, next) => {
    next(new error_1.NotFoundError(`Route ${req.originalUrl} not found`));
});
// Global error handler - must be last middleware
app.use(errorMiddleware_1.globalErrorHandler);
exports.default = app;
